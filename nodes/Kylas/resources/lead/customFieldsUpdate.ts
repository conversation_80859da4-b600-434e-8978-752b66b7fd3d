import type { INodeProperties } from 'n8n-workflow';

const showOnlyForLeadUpdate = {
    operation: ['update'],
    resource: ['lead'],
};

export const customFieldsUpdateDescription: INodeProperties[] = [
    {
        displayName: "Custom Fields",
        name: "customFields",
        type: "fixedCollection",
        placeholder: "Add Custom Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadUpdate,
        },
        description: "Add custom fields for the lead",
        typeOptions: {
            multipleValues: true,
        },
        options: [
            {
                displayName: "Property",
                name: "property",
                values: [
                    {
                        displayName: "Field Name or ID",
                        name: "name",
                        type: "options",
                        typeOptions: {
                            loadOptionsMethod: 'getLeadCustomFields',
                        },
                        default: '',
                        description: 'Choose from the list, or specify an ID using an <a href="https://docs.n8n.io/code/expressions/">expression</a>',
                    },
                    {
                        displayName: "Field Value",
                        name: "value",
                        type: "string",
                        default: '',
                        description: 'Choose from the list, or specify an ID using an <a href="https://docs.n8n.io/code/expressions/">expression</a>',
                    }
                ]
            },
        ],
    },
];
