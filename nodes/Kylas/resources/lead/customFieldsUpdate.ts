import type { INodeProperties } from 'n8n-workflow';

const showOnlyForLeadUpdate = {
    operation: ['update'],
    resource: ['lead'],
};

export const customFieldsUpdateDescription: INodeProperties[] = [
    {
        displayName: "Custom Fields",
        name: "customFields",
        type: "fixedCollection",
        placeholder: "Add Custom Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadUpdate,
        },
        description: "Add custom fields for the lead. The input type will automatically adjust based on the field type.",
        typeOptions: {
            multipleValues: true,
        },
        options: [
            {
                displayName: "Custom Field",
                name: "customField",
                values: [
                    {
                        displayName: "Field Name",
                        name: "fieldName",
                        type: "options",
                        typeOptions: {
                            loadOptionsMethod: 'getLeadCustomFields',
                        },
                        default: '',
                        description: 'Choose a custom field from the list. The field type will be shown in parentheses.',
                    },
                    // Text field input (for TEXT_FIELD, EMAIL_FIELD, URL_FIELD, TEXTAREA_FIELD, PHONE_FIELD)
                    {
                        displayName: "Value (Text)",
                        name: "textValue",
                        type: "string",
                        default: '',
                        displayOptions: {
                            show: {
                                fieldName: ['/TEXT_FIELD/', '/EMAIL_FIELD/', '/URL_FIELD/', '/TEXTAREA_FIELD/', '/PHONE_FIELD/'],
                            },
                        },
                        description: 'Enter the text value for this field',
                    },
                    // Number field input (for NUMBER_FIELD, CURRENCY_FIELD)
                    {
                        displayName: "Value (Number)",
                        name: "numberValue",
                        type: "number",
                        default: 0,
                        displayOptions: {
                            show: {
                                fieldName: ['/NUMBER_FIELD/', '/CURRENCY_FIELD/'],
                            },
                        },
                        description: 'Enter the numeric value for this field',
                    },
                    // Date field input (for DATE_FIELD)
                    {
                        displayName: "Value (Date)",
                        name: "dateValue",
                        type: "dateTime",
                        default: '',
                        displayOptions: {
                            show: {
                                fieldName: ['/DATE_FIELD/'],
                            },
                        },
                        description: 'Select the date/time value for this field',
                    },
                    // Boolean field input (for BOOLEAN_FIELD)
                    {
                        displayName: "Value (Yes/No)",
                        name: "booleanValue",
                        type: "boolean",
                        default: false,
                        displayOptions: {
                            show: {
                                fieldName: ['/BOOLEAN_FIELD/'],
                            },
                        },
                        description: 'Select true or false for this field',
                    },
                    // Picklist field input (for PICKLIST)
                    {
                        displayName: "Value (Select Option)",
                        name: "picklistValue",
                        type: "options",
                        typeOptions: {
                            loadOptionsMethod: 'getPicklistValues',
                            loadOptionsDependsOn: ['fieldName'],
                        },
                        default: '',
                        displayOptions: {
                            show: {
                                fieldName: ['/PICKLIST/'],
                            },
                        },
                        description: 'Choose an option from the picklist',
                    },
                ]
            },
        ],
    },
];
