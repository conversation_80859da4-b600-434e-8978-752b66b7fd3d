import type { INodeProperties } from 'n8n-workflow';

const showOnlyForLeadUpdate = {
    operation: ['update'],
    resource: ['lead'],
};

export const customFieldsUpdateDescription: INodeProperties[] = [
    {
        displayName: "Custom Fields",
        name: "customFields",
        type: "fixedCollection",
        placeholder: "Add Custom Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadUpdate,
        },
        description: "Add custom fields for the lead. The input type will automatically adjust based on the field type.",
        typeOptions: {
            multipleValues: true,
        },
        options: [
            {
                displayName: "Custom Field",
                name: "customField",
                values: [
                    {
                        displayName: "Field Name",
                        name: "fieldName",
                        type: "options",
                        typeOptions: {
                            loadOptionsMethod: 'getLeadCustomFields',
                        },
                        default: '',
                        description: 'Choose a custom field from the list. The field type will be shown in parentheses.',
                    },
                    // Generic value field that shows for all field types
                    {
                        displayName: "Field Value",
                        name: "value",
                        type: "string",
                        default: '',
                        description: 'Enter the value for this custom field',
                    },
                ]
            },
        ],
    },
];
